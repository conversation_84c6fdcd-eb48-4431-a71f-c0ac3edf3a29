import { NextRequest, NextResponse } from "next/server";
import { InvestigativeResearchAgentManager, PMOInvestigationRequest } from "../../../lib/agents/investigative/InvestigativeResearchAgentManager";
import { InvestigationType } from "../../../lib/agents/investigative/InvestigativeResearchAgent";
import { LlmProvider } from "../../../lib/tools/llm-tool";

interface InvestigativeResearchAPIRequest {
  action: 'preview' | 'conduct' | 'investigate' | 'history' | 'journalists' | 'types' | 'recommend-journalists'; // 'investigate' is deprecated, use 'conduct'
  pmoId?: string;
  title?: string;
  description?: string;
  investigationType?: InvestigationType;
  selectedJournalistIds?: string[];
  comparisonModels?: Array<{model: string, provider: LlmProvider}> | string[];
  criteriaModel?: string;
  optimizationModel?: string;
  assessmentModel?: string;
  consolidate?: boolean;
  consolidationModel?: string;
  userId?: string;
  priority?: 'Low' | 'Medium' | 'High' | 'Critical';
  limit?: number;
  maxJournalists?: number;
}

/**
 * API route for Investigative Research Agent
 */
export async function POST(request: NextRequest) {
  try {
    const {
      action,
      pmoId,
      title,
      description,
      investigationType,
      selectedJournalistIds,
      criteriaModel,
      optimizationModel,
      assessmentModel,
      consolidate,
      consolidationModel,
      comparisonModels,
      userId = '<EMAIL>', // Default user
      priority = 'Medium',
      limit = 10,
      maxJournalists = 3
    }: InvestigativeResearchAPIRequest = await request.json();

    // Input validation for conduct and investigate actions - TEMPORARILY DISABLED FOR TESTING
    if (false && (action === 'conduct' || action === 'investigate')) {
      console.log(`[INVESTIGATIVE_API] Validating request - Title: "${title}", Description: "${description}"`);

      // Check if title is missing or empty
      if (!title || title.trim() === '') {
        console.log(`[INVESTIGATIVE_API] Validation failed: Empty title`);
        return NextResponse.json({
          success: false,
          error: "Investigation request is too generic or contains placeholders like [text]. Please provide specific details for the investigation."
        }, { status: 400 });
      }

      // Combine title and description for placeholder detection
      const combinedText = `${title || ''} ${description || ''}`;
      console.log(`[INVESTIGATIVE_API] Combined text for validation: "${combinedText}"`);

      // Check for common placeholder patterns, but allow if there's substantial content
      const placeholderPattern = /\[.*?\]|\{.*?\}|\<.*?\>/;
      const placeholderMatches = combinedText.match(placeholderPattern);

      if (placeholderMatches) {
        // Allow if the content is substantial (more than 100 chars) and has specific details
        const contentWithoutPlaceholders = combinedText.replace(placeholderPattern, '').trim();
        const hasSubstantialContent = contentWithoutPlaceholders.length > 100;
        const hasSpecificTerms = /financial|vendor|contract|organization|analysis|investigate|identify/.test(contentWithoutPlaceholders.toLowerCase());

        if (!hasSubstantialContent || !hasSpecificTerms) {
          console.log(`[INVESTIGATIVE_API] Validation failed: Insufficient content after removing placeholders. Content: "${contentWithoutPlaceholders}"`);
          return NextResponse.json({
            success: false,
            error: "Investigation request is too generic or contains placeholders like [text]. Please provide specific details for the investigation."
          }, { status: 400 });
        } else {
          console.log(`[INVESTIGATIVE_API] Validation passed despite placeholders: Substantial content detected`);
        }
      }

      console.log(`[INVESTIGATIVE_API] Validation passed for title: "${title || 'undefined'}"`);
    }

    // Initialize the agent manager
    const agentManager = new InvestigativeResearchAgentManager({
      userId,
      defaultLlmProvider: 'openai',
      defaultLlmModel: 'o3-2025-04-16'
    });

    switch (action) {
      case 'types':
        // Get available investigation types
        const types = agentManager.getInvestigationTypes();
        return NextResponse.json({
          success: true,
          data: types
        });

      case 'journalists':
        // Get available journalists, optionally filtered by investigation type
        let journalists;
        if (investigationType) {
          journalists = agentManager.getRecommendedJournalists(investigationType);
        } else {
          journalists = agentManager.getAvailableJournalists();
        }
        
        return NextResponse.json({
          success: true,
          data: journalists
        });

      case 'recommend-journalists':
        // Get LLM-based intelligent journalist recommendations
        if (!title || !description) {
          return NextResponse.json({
            success: false,
            error: "Title and description are required for journalist recommendations"
          }, { status: 400 });
        }

        try {
          const recommendedJournalists = await agentManager.getIntelligentJournalistRecommendations(
            title,
            description,
            investigationType,
            maxJournalists
          );

          return NextResponse.json({
            success: true,
            data: recommendedJournalists
          });
        } catch (error: any) {
          console.error('Error getting journalist recommendations:', error);
          return NextResponse.json({
            success: false,
            error: error.message || "Failed to get journalist recommendations"
          }, { status: 500 });
        }

      case 'preview':
        // Preview investigation configuration
        if (!investigationType) {
          return NextResponse.json({
            success: false,
            error: "Investigation type is required for preview"
          }, { status: 400 });
        }

        const preview = agentManager.previewInvestigation({
          investigationType,
          selectedJournalistIds: selectedJournalistIds || [],
          criteriaModel,
          optimizationModel,
          assessmentModel,
          consolidate,
          consolidationModel
        });

        return NextResponse.json({
          success: true,
          data: preview
        });

      case 'conduct':
        // Conduct investigation
        if (!pmoId || !title || !investigationType) {
          return NextResponse.json({
            success: false,
            error: "PMO ID, title, and investigation type are required"
          }, { status: 400 });
        }

        const investigationRequest: PMOInvestigationRequest = {
          pmoId,
          title,
          description: description || '',
          investigationType,
          selectedJournalistIds: selectedJournalistIds || [],
          comparisonModels,
          criteriaModel,
          optimizationModel,
          assessmentModel,
          consolidate: consolidate || false,
          consolidationModel,
          userId,
          priority
        };

        console.log(`Starting investigation for PMO ${pmoId}: ${title}`);

        const result = await agentManager.conductPMOInvestigation(investigationRequest);

        console.log(`[INVESTIGATIVE_API] Investigation completed for PMO ${pmoId} - Agent_Output storage should be complete`);

        return NextResponse.json({
          success: true,
          data: result
        });

      case 'investigate':
        // DEPRECATED: Use 'conduct' action instead to avoid duplicate storage
        // This action is kept for backward compatibility but redirects to 'conduct'
        console.warn(`[INVESTIGATIVE_API] 'investigate' action is deprecated. Use 'conduct' action instead.`);

        if (!pmoId || !title) {
          return NextResponse.json({
            success: false,
            error: "PMO ID and title are required for investigation"
          }, { status: 400 });
        }

        // Redirect to 'conduct' action to avoid duplicate storage
        const conductRequest: PMOInvestigationRequest = {
          pmoId,
          title,
          description: description || '',
          investigationType: investigationType || InvestigationType.INVESTIGATIVE,
          selectedJournalistIds: selectedJournalistIds || [],
          comparisonModels,
          criteriaModel: criteriaModel || 'claude-sonnet-4-0',
          optimizationModel: optimizationModel || 'gpt-4o',
          assessmentModel: assessmentModel || 'claude-sonnet-4-0',
          consolidate: consolidate !== false,
          consolidationModel: consolidationModel || 'claude-sonnet-4-0',
          userId,
          priority
        };

        console.log(`[INVESTIGATIVE_API] Redirecting 'investigate' to 'conduct' for PMO ${pmoId}: ${title}`);

        const redirectResult = await agentManager.conductPMOInvestigation(conductRequest);

        console.log(`[INVESTIGATIVE_API] Investigation completed for PMO ${pmoId} via deprecated 'investigate' action`);

        return NextResponse.json({
          success: true,
          data: redirectResult
        });

      case 'history':
        // Get investigation history
        const history = await agentManager.getPMOInvestigationHistory(pmoId, limit);
        
        return NextResponse.json({
          success: true,
          data: history
        });

      default:
        return NextResponse.json({
          success: false,
          error: "Invalid action specified"
        }, { status: 400 });
    }

  } catch (error: any) {
    console.error("Error in investigative research API:", error);
    return NextResponse.json({
      success: false,
      error: error.message || "An error occurred during investigative research"
    }, { status: 500 });
  }
}

/**
 * GET endpoint for retrieving investigation types and journalists
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const investigationType = searchParams.get('investigationType') as InvestigationType;
    const userId = searchParams.get('userId') || '<EMAIL>';

    const agentManager = new InvestigativeResearchAgentManager({
      userId,
      defaultLlmProvider: 'openai',
      defaultLlmModel: 'o3-2025-04-16'
    });

    switch (action) {
      case 'types':
        const types = agentManager.getInvestigationTypes();
        return NextResponse.json({
          success: true,
          data: types
        });

      case 'journalists':
        let journalists;
        if (investigationType) {
          journalists = agentManager.getRecommendedJournalists(investigationType);
        } else {
          journalists = agentManager.getAvailableJournalists();
        }
        
        return NextResponse.json({
          success: true,
          data: journalists
        });

      default:
        return NextResponse.json({
          success: false,
          error: "Invalid action specified. Use 'types' or 'journalists'"
        }, { status: 400 });
    }

  } catch (error: any) {
    console.error("Error in investigative research GET API:", error);
    return NextResponse.json({
      success: false,
      error: error.message || "An error occurred"
    }, { status: 500 });
  }
}
